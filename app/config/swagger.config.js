const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

// Basic API Information
const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Reynard TBS Server API',
      version: '1.0.1',
      description: `
        Comprehensive API documentation for Reynard Toolbox Suite Server.
        
        ## Platform Usage
        - **Web**: Web application endpoints
        - **Mobile**: Mobile application endpoints  
        - **Both**: Shared endpoints used by both platforms
        
        ## Authentication
        Most endpoints require JWT authentication. Include the token in the Authorization header:
        \`Authorization: Bearer <your-jwt-token>\`
      `,
      contact: {
        name: 'WTS Energy Development Team',
        email: '<EMAIL>',
      },
      license: {
        name: 'Private License',
        url: 'https://wtsenergy.com/license',
      },
    },
    servers: [
      {
        url:
          process.env.NODE_ENV === 'production'
            ? 'https://api.wtsenergy.com/api/v1'
            : 'http://localhost:8000/api/v1',
        description:
          process.env.NODE_ENV === 'production' ? 'Production Server' : 'Development Server',
      },
      {
        url: 'http://localhost:8000/api/v2',
        description: 'Development Server - API v2',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT Authorization header using the Bearer scheme',
        },
      },
      responses: {
        UnauthorizedError: {
          description: 'Authentication information is missing or invalid',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  status: { type: 'boolean', example: false },
                  message: { type: 'string', example: 'Unauthorized access' },
                },
              },
            },
          },
        },
        ValidationError: {
          description: 'Validation error',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  status: { type: 'boolean', example: false },
                  message: { type: 'string', example: 'Required field cannot be empty.' },
                  data: {
                    type: 'object',
                    properties: {
                      error: {
                        type: 'array',
                        items: { type: 'object' },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        ServerError: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  status: { type: 'boolean', example: false },
                  message: { type: 'string', example: 'Internal server error' },
                },
              },
            },
          },
        },
      },
      schemas: {
        SuccessResponse: {
          type: 'object',
          properties: {
            status: { type: 'boolean', example: true },
            message: { type: 'string', example: 'Operation successful' },
            data: { type: 'object' },
          },
        },
        PaginationMeta: {
          type: 'object',
          properties: {
            currentPage: { type: 'integer', example: 1 },
            totalPages: { type: 'integer', example: 10 },
            totalItems: { type: 'integer', example: 100 },
            itemsPerPage: { type: 'integer', example: 10 },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization endpoints',
      },
      {
        name: 'Team Members',
        description: 'Team member management endpoints',
      },
      {
        name: 'Projects',
        description: 'Project management endpoints',
      },
      {
        name: 'Users',
        description: 'User management endpoints',
      },
      {
        name: 'Shifts',
        description: 'Shift management endpoints',
      },
      {
        name: 'Web-Platform',
        description: 'Endpoints specifically used by Web application',
      },
      {
        name: 'Mobile-Platform',
        description: 'Endpoints specifically used by Mobile application',
      },
      {
        name: 'Both-Platforms',
        description: 'Endpoints used by both Web and Mobile applications',
      },
    ],
  },
  apis: [
    './app/routes/*.js',
    './app/routes/**/*.js',
    './app/models/*.js',
    './app/docs/schemas/*.js',
  ],
};

const specs = swaggerJsdoc(options);

// Custom CSS for better UI
const customCss = `
  .swagger-ui .topbar { display: none }
  .swagger-ui .info .title { color: #2c3e50; }
  .swagger-ui .scheme-container { background: #f8f9fa; padding: 15px; border-radius: 5px; }
  .swagger-ui .tag-group { margin-bottom: 20px; }
  .swagger-ui .opblock.opblock-get .opblock-summary { border-color: #28a745; }
  .swagger-ui .opblock.opblock-post .opblock-summary { border-color: #007bff; }
  .swagger-ui .opblock.opblock-patch .opblock-summary { border-color: #ffc107; }
  .swagger-ui .opblock.opblock-delete .opblock-summary { border-color: #dc3545; }
`;

const swaggerOptions = {
  customCss,
  customSiteTitle: 'Reynard TBS API Documentation',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    docExpansion: 'none',
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    tagsSorter: 'alpha',
    operationsSorter: 'alpha',
  },
};

module.exports = {
  specs,
  swaggerUi,
  swaggerOptions,
};
