/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: User ID
 *           example: "507f1f77bcf86cd799439011"
 *         name:
 *           type: string
 *           description: User's full name
 *           example: "<PERSON>"
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *           example: "<EMAIL>"
 *         role:
 *           type: string
 *           description: User role
 *           example: "admin"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *
 *     TeamMember:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Team member ID
 *           example: "507f1f77bcf86cd799439011"
 *         name:
 *           type: string
 *           description: Team member's full name
 *           example: "<PERSON>"
 *         email:
 *           type: string
 *           format: email
 *           description: Team member's email
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           description: Phone number
 *           example: "+1234567890"
 *         position:
 *           type: string
 *           description: Job position
 *           example: "Site Engineer"
 *         project:
 *           type: string
 *           description: Associated project ID
 *           example: "507f1f77bcf86cd799439012"
 *         isActive:
 *           type: boolean
 *           description: Whether the team member is active
 *           example: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *
 *     Project:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Project ID
 *           example: "507f1f77bcf86cd799439012"
 *         name:
 *           type: string
 *           description: Project name
 *           example: "Wind Farm Construction"
 *         description:
 *           type: string
 *           description: Project description
 *           example: "Construction of offshore wind farm"
 *         status:
 *           type: string
 *           enum: [active, inactive, completed]
 *           description: Project status
 *           example: "active"
 *         startDate:
 *           type: string
 *           format: date
 *           description: Project start date
 *         endDate:
 *           type: string
 *           format: date
 *           description: Project end date
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *
 *     Error:
 *       type: object
 *       properties:
 *         status:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           example: "An error occurred"
 *         data:
 *           type: object
 *           nullable: true
 *
 *     ValidationError:
 *       type: object
 *       properties:
 *         status:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           example: "Required field cannot be empty."
 *         data:
 *           type: object
 *           properties:
 *             error:
 *               type: array
 *               items:
 *                 type: object
 *                 additionalProperties:
 *                   type: string
 *               example: [{"email": "Invalid email format"}]
 *
 *     SuccessResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "Operation completed successfully"
 *         data:
 *           type: object
 *           description: Response data (varies by endpoint)
 *
 *     PaginatedResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "Data retrieved successfully"
 *         data:
 *           type: object
 *           properties:
 *             items:
 *               type: array
 *               items: {}
 *             pagination:
 *               $ref: '#/components/schemas/PaginationMeta'
 *
 *     PaginationMeta:
 *       type: object
 *       properties:
 *         currentPage:
 *           type: integer
 *           example: 1
 *         totalPages:
 *           type: integer
 *           example: 10
 *         totalItems:
 *           type: integer
 *           example: 100
 *         itemsPerPage:
 *           type: integer
 *           example: 10
 *         hasNextPage:
 *           type: boolean
 *           example: true
 *         hasPrevPage:
 *           type: boolean
 *           example: false
 */
