/* eslint-disable no-undef */
const apiMapping = require('../../config/api-platform-mapping');

/**
 * API Registry Utility Class
 *
 * Provides utility functions to query API platform mappings
 * for testing coordination and impact analysis.
 */
class APIRegistry {
  /**
   * Get all APIs used by a specific platform
   * @param {string} platform - Platform name (web, mobile)
   * @returns {Array} Array of API endpoints with their configurations
   */
  static getAPIsByPlatform(platform) {
    return Object.entries(apiMapping)
      .filter(([_, config]) => config.platforms.includes(platform))
      .map(([endpoint, config]) => ({ endpoint, ...config }));
  }

  /**
   * Get APIs by feature
   * @param {string} feature - Feature name
   * @returns {Array} Array of API endpoints for the feature
   */
  static getAPIsByFeature(feature) {
    return Object.entries(apiMapping)
      .filter(([_, config]) => config.feature === feature)
      .map(([endpoint, config]) => ({ endpoint, ...config }));
  }

  /**
   * Get testing requirements for platform changes
   * @param {string} platform - Platform name
   * @returns {Array} Array of testing requirements
   */
  static getTestingRequirements(platform) {
    const apis = this.getAPIsByPlatform(platform);
    return apis.map(api => ({
      endpoint: api.endpoint,
      feature: api.feature,
      impact: api.impact,
      testAreas: api.testingRequired[platform] || [],
      description: api.description,
    }));
  }

  /**
   * Get high impact APIs for a platform
   * @param {string} platform - Platform name
   * @returns {Array} Array of high/critical impact APIs
   */
  static getHighImpactAPIs(platform) {
    return this.getAPIsByPlatform(platform).filter(api =>
      ['high', 'critical'].includes(api.impact)
    );
  }

  /**
   * Get APIs by owner/team
   * @param {string} owner - Team/owner name
   * @returns {Array} Array of APIs owned by the team
   */
  static getAPIsByOwner(owner) {
    return Object.entries(apiMapping)
      .filter(([_, config]) => config.owner === owner)
      .map(([endpoint, config]) => ({ endpoint, ...config }));
  }

  /**
   * Generate testing checklist for platform
   * @param {string} platform - Platform name
   * @returns {Object} Organized testing checklist
   */
  static generateTestingChecklist(platform) {
    const apis = this.getTestingRequirements(platform);
    const checklist = {
      platform,
      totalAPIs: apis.length,
      byImpact: {
        critical: apis.filter(api => api.impact === 'critical'),
        high: apis.filter(api => api.impact === 'high'),
        medium: apis.filter(api => api.impact === 'medium'),
        low: apis.filter(api => api.impact === 'low'),
      },
      byFeature: {},
    };

    // Group by feature
    apis.forEach(api => {
      if (!checklist.byFeature[api.feature]) {
        checklist.byFeature[api.feature] = [];
      }
      checklist.byFeature[api.feature].push(api);
    });

    return checklist;
  }

  /**
   * Find APIs that need testing when a specific endpoint changes
   * @param {string} endpoint - The endpoint that changed
   * @returns {Object} Related testing information
   */
  static getRelatedTestingInfo(endpoint) {
    const apiConfig = apiMapping[endpoint];
    if (!apiConfig) {
      return { found: false, message: 'API endpoint not found in mapping' };
    }

    return {
      found: true,
      endpoint,
      platforms: apiConfig.platforms,
      feature: apiConfig.feature,
      impact: apiConfig.impact,
      testingRequired: apiConfig.testingRequired,
      description: apiConfig.description,
      owner: apiConfig.owner,
    };
  }

  /**
   * Get all platforms that use a specific feature
   * @param {string} feature - Feature name
   * @returns {Array} Array of platforms using the feature
   */
  static getPlatformsByFeature(feature) {
    const apis = this.getAPIsByFeature(feature);
    const platforms = new Set();

    apis.forEach(api => {
      api.platforms.forEach(platform => platforms.add(platform));
    });

    return Array.from(platforms);
  }

  /**
   * Generate summary report
   * @returns {Object} Complete API registry summary
   */
  static generateSummaryReport() {
    const allAPIs = Object.entries(apiMapping);
    const platforms = new Set();
    const features = new Set();
    const owners = new Set();

    allAPIs.forEach(([_, config]) => {
      config.platforms.forEach(platform => platforms.add(platform));
      features.add(config.feature);
      owners.add(config.owner);
    });

    return {
      totalAPIs: allAPIs.length,
      platforms: Array.from(platforms),
      features: Array.from(features),
      owners: Array.from(owners),
      byPlatform: {
        web: this.getAPIsByPlatform('web').length,
        mobile: this.getAPIsByPlatform('mobile').length,
      },
      byImpact: {
        critical: allAPIs.filter(([_, config]) => config.impact === 'critical').length,
        high: allAPIs.filter(([_, config]) => config.impact === 'high').length,
        medium: allAPIs.filter(([_, config]) => config.impact === 'medium').length,
        low: allAPIs.filter(([_, config]) => config.impact === 'low').length,
      },
    };
  }
}

module.exports = APIRegistry;
