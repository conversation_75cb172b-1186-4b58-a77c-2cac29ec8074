const express = require('express');
const routes = express.Router();
const { verifyToken } = require('../middlewares/auth.middleware');
const validator = require('../validators/auth.validator');
const authController = require('../controllers/auth.controller');

/**
 * @swagger
 * tags:
 *   - name: Authentication
 *     description: User authentication and authorization operations
 *   - name: Both-Platforms
 *     description: Endpoints used by both web and mobile platforms
 */

/**
 * @swagger
 * /auths/login:
 *   post:
 *     summary: User login
 *     description: Authenticates user credentials and returns JWT token. Used by both web and mobile platforms.
 *     tags:
 *       - Authentication
 *       - Both-Platforms
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 description: User's password (minimum 6 characters)
 *                 example: "password123"
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         token:
 *                           type: string
 *                           description: JWT authentication token
 *                           example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                         user:
 *                           $ref: '#/components/schemas/User'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *     x-platforms: [web, mobile]
 *     x-feature: authentication
 *     x-impact: critical
 */
routes.post('/login', validator.loginValidationRule(), validator.validate, authController.login);

routes.post(
  '/forget-password',
  validator.emailValidationRule(),
  validator.validate,
  authController.forgetPassword
);

routes.patch(
  '/reset-password/:id',
  validator.resetPassValidateRule(),
  validator.validate,
  authController.resetPassword
);

routes.get('/reset-password/:resetToken', validator.validate, authController.checkResetToken);

/**
 * @swagger
 * /auths/logout:
 *   get:
 *     summary: User logout
 *     description: Logs out the authenticated user and invalidates the JWT token. Used by both platforms.
 *     tags:
 *       - Authentication
 *       - Both-Platforms
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *     x-platforms: [web, mobile]
 *     x-feature: authentication
 *     x-impact: medium
 */
routes.get('/logout', verifyToken, authController.logout);

module.exports = routes;
