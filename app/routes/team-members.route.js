// library
const express = require('express');
const routes = express.Router();

// controller
const teamMemberController = require('../controllers/team-member.controller');

// validator
const validator = require('../validators/team-member.validator');

// middleware
const { verifyToken, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

/**
 * @swagger
 * tags:
 *   - name: Team Members
 *     description: Team member management operations
 *   - name: Web-Platform
 *     description: Web application specific endpoints
 *   - name: Both-Platforms
 *     description: Endpoints used by both web and mobile platforms
 */

/**
 * @swagger
 * /team-members:
 *   post:
 *     summary: Create a new team member
 *     description: Creates a new team member in the system. Used by both web and mobile platforms.
 *     tags:
 *       - Team Members
 *       - Both-Platforms
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - project
 *             properties:
 *               name:
 *                 type: string
 *                 description: Team member's full name
 *                 example: "<PERSON>"
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Team member's email address
 *                 example: "<EMAIL>"
 *               phone:
 *                 type: string
 *                 description: Phone number
 *                 example: "+1234567890"
 *               position:
 *                 type: string
 *                 description: Job position
 *                 example: "Site Engineer"
 *               project:
 *                 type: string
 *                 description: Associated project ID
 *                 example: "507f1f77bcf86cd799439012"
 *     responses:
 *       201:
 *         description: Team member created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/TeamMember'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *     x-platforms: [web, mobile]
 *     x-feature: team-creation
 *     x-impact: high
 */
routes.post(
  '',
  verifyToken,
  validator.teamMemberValidationRule(),
  validate,
  teamMemberController.createTeamMember
);
/**
 * @swagger
 * /team-members/batch-update:
 *   patch:
 *     summary: Batch update team members
 *     description: Updates multiple team members in a single operation. Used by both platforms for bulk operations.
 *     tags:
 *       - Team Members
 *       - Both-Platforms
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               teamMembers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: Team member ID
 *                     name:
 *                       type: string
 *                       description: Updated name
 *                     email:
 *                       type: string
 *                       format: email
 *                       description: Updated email
 *                     position:
 *                       type: string
 *                       description: Updated position
 *     responses:
 *       200:
 *         description: Team members updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         updatedCount:
 *                           type: integer
 *                           example: 5
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *     x-platforms: [web, mobile]
 *     x-feature: batch-operations
 *     x-impact: medium
 */
routes.patch(
  '/batch-update',
  verifyToken,
  validator.personnelValidationRule(),
  validate,
  teamMemberController.batchUpdateTeamMembers
);

/**
 * @swagger
 * /team-members/{id}:
 *   patch:
 *     summary: Update team member details
 *     description: |
 *       Updates a specific team member's information.
 *       **Platform Usage:** Primarily used by Web Project setup-members page.
 *       This endpoint is critical for the web application's team management functionality.
 *     tags:
 *       - Team Members
 *       - Web-Platform
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Team member ID to update
 *         schema:
 *           type: string
 *           example: "507f1f77bcf86cd799439011"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Team member's full name
 *                 example: "John Doe Updated"
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Team member's email address
 *                 example: "<EMAIL>"
 *               phone:
 *                 type: string
 *                 description: Phone number
 *                 example: "+1234567890"
 *               position:
 *                 type: string
 *                 description: Job position
 *                 example: "Senior Site Engineer"
 *               isActive:
 *                 type: boolean
 *                 description: Whether the team member is active
 *                 example: true
 *     responses:
 *       200:
 *         description: Team member updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/TeamMember'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Team member not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *     x-platforms: [web]
 *     x-feature: setup-members
 *     x-impact: high
 *     x-testing-areas:
 *       web: ["setup-members page", "team management dashboard"]
 */
// (Web Project setup-members update)
routes.patch(
  '/:id',
  verifyToken,
  validator.updateTeamMemberValidationRule(),
  validate,
  teamMemberController.updateTeamMember
);
/**
 * @swagger
 * /team-members/{id}:
 *   delete:
 *     summary: Delete team member
 *     description: Soft deletes a team member from the system. Used by both platforms.
 *     tags:
 *       - Team Members
 *       - Both-Platforms
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Team member ID to delete
 *         schema:
 *           type: string
 *           example: "507f1f77bcf86cd799439011"
 *     responses:
 *       200:
 *         description: Team member deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Team member not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *     x-platforms: [web, mobile]
 *     x-feature: team-management
 *     x-impact: high
 */
routes.delete('/:id', verifyToken, deletedAt, validate, teamMemberController.deleteTeamMember);

module.exports = routes;
