# API Documentation System

This document explains how to use the comprehensive API documentation system implemented in the Reynard TBS Server.

## 🚀 Quick Start

### Access Swagger Documentation
- **Development**: http://localhost:8000/api-docs
- **JSON Export**: http://localhost:8000/api-docs.json

### Start the Server
```bash
npm run dev
```

## 📚 What's Included

### 1. Interactive Swagger UI Documentation
- **Beautiful web interface** for exploring APIs
- **Try it out** functionality to test endpoints directly
- **Platform-specific tagging** (Web, Mobile, Both)
- **Comprehensive request/response examples**
- **JWT authentication support**

### 2. Platform Identification System
Each API endpoint is tagged with:
- **Platform usage** (Web, Mobile, Both)
- **Feature categorization** (setup-members, authentication, etc.)
- **Impact level** (Critical, High, Medium, Low)
- **Testing requirements** per platform

### 3. API Registry & Query System
Command-line tools for quick API analysis:

```bash
# Get all Web platform APIs
node scripts/api-query.js --platform=web

# Get testing requirements for Web platform
node scripts/api-query.js --testing-impact --platform=web

# Get information about specific endpoint
node scripts/api-query.js --endpoint="PATCH /team-members/:id"

# Get high-impact APIs for mobile
node scripts/api-query.js --high-impact --platform=mobile

# Get complete summary
node scripts/api-query.js --summary
```

## 🏷️ Platform Tags Explained

### Web-Platform
- APIs primarily used by the web application
- Example: `PATCH /team-members/:id` (setup-members page)

### Mobile-Platform  
- APIs primarily used by the mobile application
- Example: `GET /v2/shifts` (enhanced mobile features)

### Both-Platforms
- APIs used by both web and mobile applications
- Example: `POST /auths/login` (authentication)

## 📝 Adding Documentation to New APIs

### 1. Add Swagger Comments to Route Files

```javascript
/**
 * @swagger
 * /your-endpoint:
 *   post:
 *     summary: Brief description
 *     description: Detailed description with platform usage
 *     tags: 
 *       - Your Feature
 *       - Web-Platform  # or Mobile-Platform or Both-Platforms
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               field1:
 *                 type: string
 *                 example: "example value"
 *     responses:
 *       200:
 *         description: Success response
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *     x-platforms: [web]  # or [mobile] or [web, mobile]
 *     x-feature: your-feature-name
 *     x-impact: high  # critical, high, medium, low
 */
```

### 2. Update API Platform Mapping

Add your endpoint to `config/api-platform-mapping.js`:

```javascript
'POST /your-endpoint': {
  platforms: ['web'],
  feature: 'your-feature',
  impact: 'high',
  description: 'What this API does',
  testingRequired: {
    web: ['page1', 'page2', 'functionality3']
  },
  lastModified: '2024-12-25',
  owner: 'your-team'
}
```

## 🧪 Testing Coordination

### When Backend Changes Occur

1. **Identify affected APIs**:
   ```bash
   node scripts/api-query.js --endpoint="YOUR_CHANGED_ENDPOINT"
   ```

2. **Get testing requirements**:
   ```bash
   node scripts/api-query.js --testing-impact --platform=web
   ```

3. **Coordinate with frontend teams** based on the output

### Example Workflow

```bash
# Developer changes PATCH /team-members/:id
node scripts/api-query.js --endpoint="PATCH /team-members/:id"

# Output shows:
# - Platform: web
# - Feature: setup-members  
# - Impact: HIGH
# - Test Areas: setup-members page, team management dashboard, member edit form

# Now you know exactly what to test!
```

## 🔧 Maintenance

### Updating Documentation
1. **Swagger comments** - Update directly in route files
2. **Platform mapping** - Update `config/api-platform-mapping.js`
3. **Common schemas** - Update `app/docs/schemas/common.schemas.js`

### Best Practices
- ✅ Always tag APIs with correct platform usage
- ✅ Include comprehensive examples
- ✅ Update platform mapping when adding new endpoints
- ✅ Use consistent impact levels (critical, high, medium, low)
- ✅ Keep testing requirements up-to-date

## 📊 Benefits Achieved

### For Developers
- **Clear API contracts** with request/response examples
- **Platform-specific filtering** to find relevant APIs
- **Testing coordination** made easy

### For QA Teams
- **Automated testing checklists** based on platform changes
- **Impact analysis** to prioritize testing efforts
- **Clear understanding** of which features to test

### For Project Management
- **API inventory** with platform usage
- **Impact assessment** for planning releases
- **Team ownership** clarity

## 🚀 Advanced Features

### Custom Queries
The API registry supports complex queries:

```javascript
const APIRegistry = require('./app/utils/api-registry.utils');

// Get all critical APIs
const criticalAPIs = APIRegistry.getHighImpactAPIs('web');

// Generate testing checklist
const checklist = APIRegistry.generateTestingChecklist('mobile');

// Get APIs by team
const teamAPIs = APIRegistry.getAPIsByOwner('team-management');
```

### Integration with CI/CD
You can integrate the API registry into your CI/CD pipeline to:
- Generate testing reports
- Validate API documentation completeness
- Create deployment checklists

## 📞 Support

For questions about the API documentation system:
1. Check this README first
2. Use the CLI tools for quick queries
3. Refer to the Swagger UI for detailed API information
4. Contact the development team for system updates
