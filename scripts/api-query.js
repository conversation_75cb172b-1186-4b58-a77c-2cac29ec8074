#!/usr/bin/env node

/**
 * API Query CLI Tool
 *
 * Usage:
 * node scripts/api-query.js --platform=web
 * node scripts/api-query.js --feature=team-management
 * node scripts/api-query.js --testing-impact --platform=web
 * node scripts/api-query.js --summary
 * node scripts/api-query.js --endpoint="PATCH /team-members/:id"
 */

const APIRegistry = require('../app/utils/api-registry.utils');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

args.forEach(arg => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.substring(2).split('=');
    options[key] = value || true;
  }
});

// Console styling
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(`  ${title}`, 'bright'));
  console.log(colorize('='.repeat(60), 'cyan') + '\n');
}

function printAPIs(apis, title) {
  if (apis.length === 0) {
    console.log(colorize('No APIs found.', 'yellow'));
    return;
  }

  console.log(colorize(`📱 ${title}:`, 'bright'));
  apis.forEach(api => {
    const impactColor =
      {
        critical: 'red',
        high: 'yellow',
        medium: 'blue',
        low: 'green',
      }[api.impact] || 'reset';

    console.log(
      `├── ${colorize(api.endpoint, 'cyan')} (${api.feature}) - ${colorize(
        api.impact.toUpperCase() + ' IMPACT',
        impactColor
      )}`
    );
    console.log(`│   ${api.description}`);
    if (api.platforms) {
      console.log(`│   Platforms: ${colorize(api.platforms.join(', '), 'magenta')}`);
    }
    console.log('');
  });
}

function printTestingRequirements(platform) {
  const requirements = APIRegistry.getTestingRequirements(platform);

  if (requirements.length === 0) {
    console.log(colorize('No testing requirements found.', 'yellow'));
    return;
  }

  console.log(colorize(`🧪 Testing Required for ${platform.toUpperCase()} Platform:`, 'bright'));

  requirements.forEach(req => {
    const impactColor =
      {
        critical: 'red',
        high: 'yellow',
        medium: 'blue',
        low: 'green',
      }[req.impact] || 'reset';

    console.log(
      `├── ${colorize(req.endpoint, 'cyan')} - ${colorize(req.impact.toUpperCase(), impactColor)}`
    );
    console.log(`│   Feature: ${req.feature}`);
    console.log(`│   Description: ${req.description}`);

    if (req.testAreas.length > 0) {
      console.log(`│   Test Areas: ${colorize(req.testAreas.join(', '), 'green')}`);
    }
    console.log('');
  });
}

function printSummary() {
  const summary = APIRegistry.generateSummaryReport();

  console.log(colorize('📊 API Registry Summary:', 'bright'));
  console.log(`├── Total APIs: ${colorize(summary.totalAPIs, 'cyan')}`);
  console.log(`├── Platforms: ${colorize(summary.platforms.join(', '), 'magenta')}`);
  console.log(`├── Features: ${colorize(summary.features.length, 'blue')}`);
  console.log(`└── Teams: ${colorize(summary.owners.length, 'green')}`);

  console.log('\n' + colorize('📱 By Platform:', 'bright'));
  console.log(`├── Web: ${colorize(summary.byPlatform.web, 'cyan')} APIs`);
  console.log(`└── Mobile: ${colorize(summary.byPlatform.mobile, 'cyan')} APIs`);

  console.log('\n' + colorize('⚡ By Impact:', 'bright'));
  console.log(`├── Critical: ${colorize(summary.byImpact.critical, 'red')} APIs`);
  console.log(`├── High: ${colorize(summary.byImpact.high, 'yellow')} APIs`);
  console.log(`├── Medium: ${colorize(summary.byImpact.medium, 'blue')} APIs`);
  console.log(`└── Low: ${colorize(summary.byImpact.low, 'green')} APIs`);
}

function printEndpointInfo(endpoint) {
  const info = APIRegistry.getRelatedTestingInfo(endpoint);

  if (!info.found) {
    console.log(colorize(`❌ ${info.message}`, 'red'));
    return;
  }

  console.log(colorize(`🔍 API Endpoint Information:`, 'bright'));
  console.log(`├── Endpoint: ${colorize(info.endpoint, 'cyan')}`);
  console.log(`├── Platforms: ${colorize(info.platforms.join(', '), 'magenta')}`);
  console.log(`├── Feature: ${colorize(info.feature, 'blue')}`);
  console.log(
    `├── Impact: ${colorize(
      info.impact.toUpperCase(),
      info.impact === 'critical' ? 'red' : 'yellow'
    )}`
  );
  console.log(`├── Owner: ${colorize(info.owner, 'green')}`);
  console.log(`└── Description: ${info.description}`);

  if (info.testingRequired) {
    console.log('\n' + colorize('🧪 Testing Requirements:', 'bright'));
    Object.entries(info.testingRequired).forEach(([platform, areas]) => {
      console.log(`├── ${colorize(platform.toUpperCase(), 'magenta')}: ${areas.join(', ')}`);
    });
  }
}

// Main execution
function main() {
  if (options.help || Object.keys(options).length === 0) {
    printHeader('API Query CLI Tool - Help');
    console.log('Usage examples:');
    console.log('  node scripts/api-query.js --platform=web');
    console.log('  node scripts/api-query.js --feature=team-management');
    console.log('  node scripts/api-query.js --testing-impact --platform=web');
    console.log('  node scripts/api-query.js --summary');
    console.log('  node scripts/api-query.js --endpoint="PATCH /team-members/:id"');
    console.log('  node scripts/api-query.js --high-impact --platform=mobile');
    return;
  }

  if (options.summary) {
    printHeader('API Registry Summary Report');
    printSummary();
    return;
  }

  if (options.endpoint) {
    printHeader(`Endpoint Information: ${options.endpoint}`);
    printEndpointInfo(options.endpoint);
    return;
  }

  if (options.platform) {
    if (options['testing-impact']) {
      printHeader(`Testing Impact Analysis - ${options.platform.toUpperCase()} Platform`);
      printTestingRequirements(options.platform);
    } else if (options['high-impact']) {
      printHeader(`High Impact APIs - ${options.platform.toUpperCase()} Platform`);
      const apis = APIRegistry.getHighImpactAPIs(options.platform);
      printAPIs(apis, `High Impact ${options.platform.toUpperCase()} APIs`);
    } else {
      printHeader(`${options.platform.toUpperCase()} Platform APIs`);
      const apis = APIRegistry.getAPIsByPlatform(options.platform);
      printAPIs(apis, `${options.platform.toUpperCase()} Platform APIs`);
    }
    return;
  }

  if (options.feature) {
    printHeader(`Feature APIs: ${options.feature}`);
    const apis = APIRegistry.getAPIsByFeature(options.feature);
    printAPIs(apis, `${options.feature} Feature APIs`);
    return;
  }

  console.log(colorize('❌ Invalid options. Use --help for usage information.', 'red'));
}

main();
