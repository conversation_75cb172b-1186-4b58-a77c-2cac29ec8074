/**
 * API Platform Mapping Configuration
 *
 * This file maps API endpoints to their respective platforms and provides
 * metadata for testing coordination and impact analysis.
 *
 * Usage:
 * - Use this for automated testing coordination
 * - Reference when making backend changes
 * - Generate testing checklists
 * - Platform-specific deployment planning
 */

module.exports = {
  // Authentication APIs
  'POST /auths/login': {
    platforms: ['web', 'mobile'],
    feature: 'authentication',
    impact: 'critical',
    description: 'User login with JWT token generation',
    testingRequired: {
      web: ['login page', 'session management'],
      mobile: ['login screen', 'token storage'],
    },
    lastModified: '2024-12-25',
    owner: 'auth-team',
  },

  'GET /auths/logout': {
    platforms: ['web', 'mobile'],
    feature: 'authentication',
    impact: 'medium',
    description: 'User logout and token invalidation',
    testingRequired: {
      web: ['logout functionality', 'session cleanup'],
      mobile: ['logout flow', 'token cleanup'],
    },
    lastModified: '2024-12-25',
    owner: 'auth-team',
  },

  'POST /auths/forget-password': {
    platforms: ['web', 'mobile'],
    feature: 'password-recovery',
    impact: 'high',
    description: 'Initiate password reset process',
    testingRequired: {
      web: ['forgot password form', 'email sending'],
      mobile: ['password reset screen', 'email verification'],
    },
    lastModified: '2024-12-25',
    owner: 'auth-team',
  },

  // Team Members APIs
  'POST /team-members': {
    platforms: ['web', 'mobile'],
    feature: 'team-creation',
    impact: 'high',
    description: 'Create new team member',
    testingRequired: {
      web: ['add member form', 'team management dashboard'],
      mobile: ['team setup screen', 'member creation flow'],
    },
    lastModified: '2024-12-25',
    owner: 'team-management',
  },

  'PATCH /team-members/:id': {
    platforms: ['web'],
    feature: 'setup-members',
    impact: 'high',
    description: 'Update team member details - Web Project setup-members page',
    testingRequired: {
      web: ['setup-members page', 'team management dashboard', 'member edit form'],
    },
    lastModified: '2024-12-25',
    owner: 'team-management',
    notes: 'Primary usage in Web Project setup-members functionality',
  },

  'PATCH /team-members/batch-update': {
    platforms: ['web', 'mobile'],
    feature: 'batch-operations',
    impact: 'medium',
    description: 'Bulk update multiple team members',
    testingRequired: {
      web: ['bulk edit interface', 'team management dashboard'],
      mobile: ['batch operations screen'],
    },
    lastModified: '2024-12-25',
    owner: 'team-management',
  },

  'DELETE /team-members/:id': {
    platforms: ['web', 'mobile'],
    feature: 'team-management',
    impact: 'high',
    description: 'Soft delete team member',
    testingRequired: {
      web: ['delete confirmation', 'team list updates'],
      mobile: ['member removal flow', 'list refresh'],
    },
    lastModified: '2024-12-25',
    owner: 'team-management',
  },

  // Projects APIs (examples for future documentation)
  'GET /projects': {
    platforms: ['web', 'mobile'],
    feature: 'project-listing',
    impact: 'high',
    description: 'Get list of projects',
    testingRequired: {
      web: ['project dashboard', 'project selection'],
      mobile: ['project list screen', 'project switching'],
    },
    lastModified: '2024-12-25',
    owner: 'project-management',
  },

  'POST /projects': {
    platforms: ['web'],
    feature: 'project-creation',
    impact: 'high',
    description: 'Create new project',
    testingRequired: {
      web: ['project creation form', 'project dashboard'],
    },
    lastModified: '2024-12-25',
    owner: 'project-management',
  },

  // Shifts APIs (examples)
  'GET /shifts': {
    platforms: ['mobile', 'web'],
    feature: 'shift-management',
    impact: 'critical',
    description: 'Get shift schedules',
    testingRequired: {
      web: ['shift calendar', 'schedule view'],
      mobile: ['shift list', 'schedule screen'],
    },
    lastModified: '2024-12-25',
    owner: 'shift-management',
  },

  'POST /shifts': {
    platforms: ['web'],
    feature: 'shift-creation',
    impact: 'high',
    description: 'Create new shift',
    testingRequired: {
      web: ['shift creation form', 'calendar integration'],
    },
    lastModified: '2024-12-25',
    owner: 'shift-management',
  },

  // Version 2 APIs
  'GET /v2/shifts': {
    platforms: ['mobile'],
    feature: 'shift-management-v2',
    impact: 'critical',
    description: 'Enhanced shift management for mobile',
    testingRequired: {
      mobile: ['new shift interface', 'enhanced features'],
    },
    lastModified: '2024-12-25',
    owner: 'shift-management',
    version: 'v2',
  },

  'GET /v2/team-members': {
    platforms: ['mobile'],
    feature: 'team-management-v2',
    impact: 'high',
    description: 'Enhanced team member management for mobile',
    testingRequired: {
      mobile: ['improved team interface', 'new features'],
    },
    lastModified: '2024-12-25',
    owner: 'team-management',
    version: 'v2',
  },
};
